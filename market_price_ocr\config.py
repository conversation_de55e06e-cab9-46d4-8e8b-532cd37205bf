# Configuration for Market Price OCR
# Adjust these coordinates as needed

# Inventory Grid Configuration
FIRST_SLOT_COORDS = (1478, 230)  # Top-left inventory slot coordinates
SLOT_OFFSET_X = 59                # Horizontal distance between slots
SLOT_OFFSET_Y = 59                # Vertical distance between slots

# Market Configuration
OPEN_MARKET_COORDS = (1872, 626)  # Right-click to open market (inventory slot with market item)
MARKET_NAVIGATE_COORDS = (292, 78)  # Left-click to navigate to drop area after opening market
CLOSE_MARKET_COORDS = (890, 43)   # Left-click to close market (X button)
MARKET_DROP_ZONE = (97, 215)      # Where to drop items in market interface
PRICE_OCR_AREA = (36, 343, 129, 59)  # Area to capture for price OCR (left, top, width, height)

# Timing Configuration (milliseconds)
CLICK_DELAY_MS = 500              # Delay between clicks
OCR_DELAY_MS = 500                # Delay before OCR after dropping item

# Scanning Configuration
RETRY_ATTEMPTS = 3                # Number of OCR retry attempts per slot
GRID_SIZE = 8                     # 8x8 inventory grid (64 slots total)

# GitHub Auto-Push Configuration
GITHUB_REPO_PATH = r"C:\path\to\your\plugin\repo"  # ← CHANGE THIS to your plugin repo path
GITHUB_TARGET_FILE = "data/prices.json"            # Path within repo where prices.json goes
ENABLE_GITHUB_PUSH = False                          # Set to True to enable auto-push
