# Market price data storage and JSON handling

import json
import os
from datetime import datetime

class MarketDataManager:
    def __init__(self, data_file="market_prices.json"):
        """Initialize market data manager"""
        self.data_file = data_file
        self.price_data = {}
        self.load_data()

    def load_data(self):
        """Load existing price data from JSON file"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    self.price_data = json.load(f)
            else:
                self.price_data = {}
        except Exception as e:
            print(f"Error loading market data: {e}")
            self.price_data = {}

    def save_data(self):
        """Save price data to JSON file"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.price_data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Error saving market data: {e}")
            return False

    def add_price_data(self, slot_number, slot_name, price_info):
        """
        Add price data for a specific slot (overwrites previous data)
        Args:
            slot_number: Integer slot number (1-64)
            slot_name: Display name for the slot
            price_info: Dictionary with price information from OCR
        """
        timestamp = datetime.now().isoformat()

        slot_key = f"slot_{slot_number}"

        # Store only the latest price data (no history)
        self.price_data[slot_key] = {
            'slot_number': slot_number,
            'slot_name': slot_name,
            'timestamp': timestamp,
            'min_price': price_info.get('min_price'),
            'weekly_avg': price_info.get('weekly_avg'),
            'raw_text': price_info.get('raw_text', ''),
            'cleaned_text': price_info.get('cleaned_text', '')
        }

    def get_latest_price(self, slot_number):
        """Get the latest price data for a slot"""
        slot_key = f"slot_{slot_number}"

        if slot_key in self.price_data:
            return self.price_data[slot_key]
        return None

    def get_all_latest_prices(self):
        """Get latest prices for all slots that have data"""
        latest_prices = {}

        for slot_key, slot_data in self.price_data.items():
            latest_prices[slot_key] = {
                'slot_number': slot_data['slot_number'],
                'slot_name': slot_data['slot_name'],
                'latest_price': slot_data
            }

        return latest_prices

    def clear_slot_data(self, slot_number):
        """Clear all data for a specific slot"""
        slot_key = f"slot_{slot_number}"
        if slot_key in self.price_data:
            del self.price_data[slot_key]

    def clear_all_data(self):
        """Clear all price data"""
        self.price_data = {}

    def export_to_file(self, filename=None):
        """Export current data to a specific file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"market_prices_export_{timestamp}.json"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.price_data, f, indent=2, ensure_ascii=False)
            return filename
        except Exception as e:
            print(f"Error exporting data: {e}")
            return None

    def get_scan_progress(self):
        """Get scanning progress information"""
        total_slots = 64
        scanned_slots = len(self.price_data)

        return {
            'total_slots': total_slots,
            'scanned_slots': scanned_slots,
            'progress_percent': (scanned_slots / total_slots) * 100,
            'remaining_slots': total_slots - scanned_slots
        }
