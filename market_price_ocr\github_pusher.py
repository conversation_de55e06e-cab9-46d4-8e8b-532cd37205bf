# GitHub auto-push functionality for price updates

import subprocess
import os
import shutil
from datetime import datetime

class GitHubPusher:
    def __init__(self, repo_path, target_file_path="data/prices.json"):
        """
        Initialize GitHub pusher
        Args:
            repo_path: Path to your plugin repository
            target_file_path: Path within repo where prices.json should go
        """
        self.repo_path = repo_path
        self.target_file_path = target_file_path
        
    def copy_and_push_prices(self, source_file="website_prices.json"):
        """
        Copy prices file to repo and push to GitHub
        Args:
            source_file: Local prices file to copy
        Returns:
            True if successful, False otherwise
        """
        try:
            # Check if source file exists
            if not os.path.exists(source_file):
                print(f"❌ Source file {source_file} not found")
                return False
                
            # Check if repo directory exists
            if not os.path.exists(self.repo_path):
                print(f"❌ Repository path {self.repo_path} not found")
                return False
                
            # Full path to target file in repo
            target_full_path = os.path.join(self.repo_path, self.target_file_path)
            target_dir = os.path.dirname(target_full_path)
            
            # Create target directory if it doesn't exist
            if not os.path.exists(target_dir):
                os.makedirs(target_dir)
                print(f"📁 Created directory: {target_dir}")
            
            # Copy file to repo
            shutil.copy2(source_file, target_full_path)
            print(f"📋 Copied {source_file} to {target_full_path}")
            
            # Change to repo directory
            original_dir = os.getcwd()
            os.chdir(self.repo_path)
            
            try:
                # Git operations
                print("🔄 Adding file to git...")
                subprocess.run(["git", "add", self.target_file_path], check=True)
                
                commit_message = f"Auto-update prices {datetime.now().strftime('%Y-%m-%d %H:%M')}"
                print(f"💾 Committing with message: {commit_message}")
                subprocess.run(["git", "commit", "-m", commit_message], check=True)
                
                print("🚀 Pushing to GitHub...")
                subprocess.run(["git", "push"], check=True)
                
                print("✅ Prices pushed to GitHub successfully!")
                return True
                
            except subprocess.CalledProcessError as e:
                print(f"❌ Git operation failed: {e}")
                return False
            finally:
                # Always return to original directory
                os.chdir(original_dir)
                
        except Exception as e:
            print(f"❌ Error during push operation: {e}")
            return False
    
    def check_git_status(self):
        """Check if git repo is in good state"""
        try:
            original_dir = os.getcwd()
            os.chdir(self.repo_path)
            
            # Check if it's a git repo
            result = subprocess.run(["git", "status"], capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ Not a valid git repository")
                return False
                
            print("✅ Git repository status OK")
            return True
            
        except Exception as e:
            print(f"❌ Error checking git status: {e}")
            return False
        finally:
            os.chdir(original_dir)
