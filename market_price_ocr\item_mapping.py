# Item name mapping for each inventory slot
# Edit the item names below to match your inventory layout

SLOT_TO_ITEM_MAPPING = {
    1: "Force Core(Low)",
    2: "Force Core(Medium)",
    3: "Force Core(High)", 
    4: "Force Core(Highest)", 
    5: "Force Core(Ultimate)", 
    6: "Upgrade Core(Low)", 
    7: "Upgrade Core(Medium)", 
    8: "Upgrade Core(High)", 
    9: "Upgrade Core(Highest)",  
    10: "Upgrade Core(Ultimate)",
    11: "Upgrade Core Set(Low)", # Add item name here
    12: "Upgrade Core Set(Medium)", # Add item name here
    13: "Upgrade Core Set(High)", # Add item name here
    14: "Upgrade Core Set(Highest)", # Add item name here
    15: "Upgrade Core Set(Ultimate)", # Add item name here
    16: "Force Essence", # Add item name here
    17: "Quartz Core(Bluestin)", # Add item name here
    18: "Quartz Core(Pherystin)", # Add item name here
    19: "Quartz Core(Aqua)", # Add item name here
    20: "Quartz Core(Lapis)", # Add item name here
    21: "Quartz Core(Topaz)", # Add item name here
    22: "Quartz Core(SigMetal)", # Add item name here
    23: "Quartz Core(Mithril)", # Add item name here
    24: "Quartz Core(Archridium)", # Add item name here
    25: "Quartz Core(Palladium)", # Add item name here
    26: "Material Core(Bluestin)", # Add item name here
    27: "Material Core(Titanium)", # Add item name here
    28: "Material Core(Shadow Titanium)", # Add item name here
    29: "Material Core(Osmium)", # Add item name here
    30: "Material Core(Red Osmium)", # Add item name here
    31: "Material Core(SigMetal)", # Add item name here
    32: "Material Core(Mithril)", # Add item name here
    33: "Material Core(Archridium)", # Add item name here
    34: "Upgrade Core(Piece)", # Add item name here
    35: "Force Core(Piece)", # Add item name here
    36: "Upgrade Core(Crystal)", # Add item name here
    37: "Force Core(Crystal)", # Add item name here
    38: "Devil's Token(High)", # Add item name here
    39: "Devil's Token(Highest)", # Add item name here
    40: "Divine Core", # Add item name here
    41: "Chaos Core", # Add item name here
    42: "Shape Cartridge(Lv. 2)", # Add item name here
    43: "Shape Cartridge(Lv. 3)", # Add item name here
    44: "Shape Cartridge(Lv. 4)", # Add item name here
    45: "Extender Circuit", # Add item name here
    46: "Slot Extender (Low)", # Add item name here
    47: "Merit Medal Exchange Ticket", # Add item name here
    48: "Sword Damage Amplifier(Lv. 1)", # Add item name here
    49: "Sword Damage Amplifier(Lv. 2)", # Add item name here
    50: "Sword Damage Amplifier(Lv. 3)", # Add item name here
    51: "Sword Damage Amplifier(Lv. 4)", # Add item name here
    52: "Sword Damage Amplifier(Lv. 5)", # Add item name here
    53: "Sword Damage Amplifier(Lv. 6)", # Add item name here
    54: "Sword Damage Amplifier(Lv. 7)", # Add item name here
    55: "", # Add item name here
    56: "", # Add item name here
    57: "", # Add item name here
    58: "", # Add item name here
    59: "", # Add item name here
    60: "", # Add item name here
    61: "", # Add item name here
    62: "", # Add item name here
    63: "", # Add item name here
    64: "", # Add item name here
}

# Configuration for output format
USE_MIN_PRICE = True  # True = use min_price, False = use weekly_avg
SKIP_EMPTY_SLOTS = True  # True = skip slots with empty item names
