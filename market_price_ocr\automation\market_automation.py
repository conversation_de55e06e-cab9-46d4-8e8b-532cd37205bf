# Market price scanning automation

import time
import threading
from tkinter import messagebox

class MarketAutomation:
    def __init__(self, game_connector, ocr_engine, inventory_grid, market_data, status_callback=None):
        """Initialize market price automation"""
        self.game_connector = game_connector
        self.ocr_engine = ocr_engine
        self.inventory_grid = inventory_grid
        self.market_data = market_data
        self.status_callback = status_callback

        # Automation state
        self.running = False
        self.current_slot = 1
        self.total_slots = 64

        # Configuration
        self.market_drop_zone = None  # Where to drop items in market UI
        self.price_ocr_area = None    # Area to OCR for price information
        self.click_delay_ms = 500     # Delay between clicks
        self.ocr_delay_ms = 1000      # Delay before OCR after dropping item
        self.retry_attempts = 3       # Number of OCR retry attempts

    def update_status(self, message):
        """Update status via callback if available"""
        if self.status_callback:
            self.status_callback(message)

    def set_market_drop_zone(self, coords):
        """Set the market drop zone coordinates"""
        self.market_drop_zone = coords

    def set_price_ocr_area(self, area):
        """Set the area for price OCR"""
        self.price_ocr_area = area

    def set_delays(self, click_delay_ms, ocr_delay_ms):
        """Set timing delays"""
        self.click_delay_ms = click_delay_ms
        self.ocr_delay_ms = ocr_delay_ms

    def is_configured(self):
        """Check if automation is properly configured"""
        return (self.inventory_grid.is_configured() and
                self.market_drop_zone is not None and
                self.price_ocr_area is not None)

    def start_scan(self, start_slot=1, end_slot=64):
        """Start market price scanning"""
        if not self.is_configured():
            messagebox.showerror("Configuration Error",
                               "Please configure inventory grid, market drop zone, and price OCR area first!")
            return False

        if not self.game_connector.is_connected():
            if not self.game_connector.connect_to_game():
                messagebox.showerror("Connection Error",
                                   "Could not connect to the game window. Make sure the game is running.")
                return False

        self.current_slot = start_slot
        self.total_slots = end_slot
        self.running = True

        self.update_status(f"Starting market scan from slot {start_slot} to {end_slot}")

        # Start scanning in a separate thread
        threading.Thread(target=self._scan_loop, daemon=True).start()
        return True

    def stop_scan(self):
        """Stop the market scanning"""
        self.running = False
        self.update_status("Market scan stopped")

    def _scan_loop(self):
        """Main scanning loop"""
        try:
            while self.running and self.current_slot <= self.total_slots:
                success = self._scan_slot(self.current_slot)

                if success:
                    self.update_status(f"Slot {self.current_slot} scanned successfully")
                else:
                    self.update_status(f"Failed to scan slot {self.current_slot}")

                self.current_slot += 1

                # Small delay between slots
                time.sleep(self.click_delay_ms / 1000.0)

            if self.running:  # Completed normally
                self.update_status("Market scan completed!")
                self.market_data.save_data()
                messagebox.showinfo("Scan Complete",
                                  f"Market price scan completed! Data saved to {self.market_data.data_file}")

        except Exception as e:
            self.update_status(f"Error in scan loop: {str(e)}")
            messagebox.showerror("Scan Error", f"An error occurred during scanning:\n{str(e)}")
        finally:
            self.running = False

    def _scan_slot(self, slot_number):
        """Scan a specific inventory slot for market price"""
        try:
            # Get slot coordinates
            slot_coords = self.inventory_grid.get_slot_coordinates(slot_number)
            if not slot_coords:
                self.update_status(f"❌ Could not get coordinates for slot {slot_number}")
                return False

            self.update_status(f"🎯 Scanning slot {slot_number} at coordinates {slot_coords}")

            # Click on inventory slot
            if not self.game_connector.click_at_position(slot_coords):
                self.update_status(f"❌ Failed to click slot {slot_number} at {slot_coords}")
                return False

            time.sleep(self.click_delay_ms / 1000.0)

            # Click on market drop zone
            if not self.game_connector.click_at_position(self.market_drop_zone):
                self.update_status(f"❌ Failed to click market drop zone {self.market_drop_zone} for slot {slot_number}")
                return False

            # Wait for market UI to update
            time.sleep(self.ocr_delay_ms / 1000.0)

            # Perform OCR with retry logic
            price_info = self._ocr_with_retry()

            if price_info:
                # Save price data
                slot_name = self.inventory_grid.get_slot_name(slot_number)
                self.market_data.add_price_data(slot_number, slot_name, price_info)

                self.update_status(f"✅ Slot {slot_number}: Weekly Avg={price_info.get('weekly_avg', 'N/A')}, "
                                 f"Min={price_info.get('min_price', 'N/A')}")
                return True
            else:
                self.update_status(f"❌ Could not extract price data for slot {slot_number}")
                return False

        except Exception as e:
            self.update_status(f"❌ Error scanning slot {slot_number}: {str(e)}")
            return False

    def _ocr_with_retry(self):
        """Perform OCR with retry logic for better accuracy"""
        for attempt in range(self.retry_attempts):
            try:
                # Capture price area
                screenshot = self.game_connector.capture_area_bitblt(self.price_ocr_area)
                if screenshot is None:
                    continue

                # Extract text
                raw_text = self.ocr_engine.extract_text(screenshot)

                # Extract price information
                price_info = self.ocr_engine.extract_prices(raw_text)

                if price_info and price_info.get('min_price'):
                    return price_info

                # If no valid price found, wait a bit and retry
                if attempt < self.retry_attempts - 1:
                    time.sleep(0.3)

            except Exception as e:
                self.update_status(f"OCR attempt {attempt + 1} failed: {str(e)}")

        return None

    def get_progress(self):
        """Get current scanning progress"""
        if not self.running:
            return None

        return {
            'current_slot': self.current_slot,
            'total_slots': self.total_slots,
            'progress_percent': ((self.current_slot - 1) / self.total_slots) * 100,
            'remaining_slots': self.total_slots - self.current_slot + 1
        }
