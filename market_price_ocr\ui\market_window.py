# Main UI window for market price OCR

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os

class MarketWindow:
    def __init__(self, game_connector, ocr_engine, inventory_grid, market_data, market_automation, area_selector):
        """Initialize the main market window"""
        self.game_connector = game_connector
        self.ocr_engine = ocr_engine
        self.inventory_grid = inventory_grid
        self.market_data = market_data
        self.market_automation = market_automation
        self.area_selector = area_selector

        # Create main window
        self.root = tk.Tk()
        self.root.title("Market Price OCR Tool")
        self.root.geometry("800x600")

        # Status text
        self.status_text = tk.StringVar()
        self.status_text.set("Ready")

        self.create_widgets()
        self.update_status_display()

    def create_widgets(self):
        """Create all UI widgets"""
        # Main notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # Configuration tab
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="Configuration")
        self.create_config_tab(config_frame)

        # Scanning tab
        scan_frame = ttk.Frame(notebook)
        notebook.add(scan_frame, text="Scanning")
        self.create_scan_tab(scan_frame)

        # Results tab
        results_frame = ttk.Frame(notebook)
        notebook.add(results_frame, text="Results")
        self.create_results_tab(results_frame)

        # Status bar
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill="x", padx=10, pady=(0, 10))

        ttk.Label(status_frame, text="Status:").pack(side="left")
        self.status_label = ttk.Label(status_frame, textvariable=self.status_text)
        self.status_label.pack(side="left", padx=(5, 0))

    def create_config_tab(self, parent):
        """Create configuration tab"""
        # Game connection
        conn_frame = ttk.LabelFrame(parent, text="Game Connection")
        conn_frame.pack(fill="x", padx=10, pady=5)

        ttk.Button(conn_frame, text="Connect to Game",
                  command=self.connect_to_game).pack(side="left", padx=5, pady=5)

        self.conn_status = ttk.Label(conn_frame, text="Not connected")
        self.conn_status.pack(side="left", padx=10)

        # Inventory grid configuration
        inv_frame = ttk.LabelFrame(parent, text="Inventory Grid Configuration")
        inv_frame.pack(fill="x", padx=10, pady=5)

        ttk.Button(inv_frame, text="Select First Slot (Top-Left)",
                  command=self.select_first_slot).pack(pady=2)

        # Offset inputs
        offset_frame = ttk.Frame(inv_frame)
        offset_frame.pack(fill="x", pady=5)

        ttk.Label(offset_frame, text="Slot Offset X:").grid(row=0, column=0, padx=5)
        self.offset_x_var = tk.StringVar(value="50")
        ttk.Entry(offset_frame, textvariable=self.offset_x_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(offset_frame, text="Slot Offset Y:").grid(row=0, column=2, padx=5)
        self.offset_y_var = tk.StringVar(value="50")
        ttk.Entry(offset_frame, textvariable=self.offset_y_var, width=10).grid(row=0, column=3, padx=5)

        ttk.Button(offset_frame, text="Apply Offsets",
                  command=self.apply_offsets).grid(row=0, column=4, padx=10)

        # Market configuration
        market_frame = ttk.LabelFrame(parent, text="Market Configuration")
        market_frame.pack(fill="x", padx=10, pady=5)

        ttk.Button(market_frame, text="Select Market Drop Zone",
                  command=self.select_market_drop_zone).pack(pady=2)

        ttk.Button(market_frame, text="Select Price OCR Area",
                  command=self.select_price_ocr_area).pack(pady=2)

        # Timing configuration
        timing_frame = ttk.LabelFrame(parent, text="Timing Configuration (milliseconds)")
        timing_frame.pack(fill="x", padx=10, pady=5)

        timing_grid = ttk.Frame(timing_frame)
        timing_grid.pack(fill="x", pady=5)

        ttk.Label(timing_grid, text="Click Delay:").grid(row=0, column=0, padx=5)
        self.click_delay_var = tk.StringVar(value="500")
        ttk.Entry(timing_grid, textvariable=self.click_delay_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(timing_grid, text="OCR Delay:").grid(row=0, column=2, padx=5)
        self.ocr_delay_var = tk.StringVar(value="1000")
        ttk.Entry(timing_grid, textvariable=self.ocr_delay_var, width=10).grid(row=0, column=3, padx=5)

        # Configuration status
        self.config_status = ttk.Label(parent, text="Configuration incomplete")
        self.config_status.pack(pady=10)

    def create_scan_tab(self, parent):
        """Create scanning tab"""
        # Scan controls
        control_frame = ttk.LabelFrame(parent, text="Scan Controls")
        control_frame.pack(fill="x", padx=10, pady=5)

        # Slot range
        range_frame = ttk.Frame(control_frame)
        range_frame.pack(fill="x", pady=5)

        ttk.Label(range_frame, text="Start Slot:").grid(row=0, column=0, padx=5)
        self.start_slot_var = tk.StringVar(value="1")
        ttk.Entry(range_frame, textvariable=self.start_slot_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(range_frame, text="End Slot:").grid(row=0, column=2, padx=5)
        self.end_slot_var = tk.StringVar(value="64")
        ttk.Entry(range_frame, textvariable=self.end_slot_var, width=10).grid(row=0, column=3, padx=5)

        # Scan buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill="x", pady=5)

        self.start_button = ttk.Button(button_frame, text="Start Scan", command=self.start_scan)
        self.start_button.pack(side="left", padx=5)

        self.stop_button = ttk.Button(button_frame, text="Stop Scan", command=self.stop_scan, state="disabled")
        self.stop_button.pack(side="left", padx=5)

        # Progress
        progress_frame = ttk.LabelFrame(parent, text="Progress")
        progress_frame.pack(fill="x", padx=10, pady=5)

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill="x", padx=10, pady=5)

        self.progress_label = ttk.Label(progress_frame, text="Ready to scan")
        self.progress_label.pack(pady=5)

    def create_results_tab(self, parent):
        """Create results tab"""
        # Results display
        results_frame = ttk.LabelFrame(parent, text="Scan Results")
        results_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # Treeview for results
        columns = ("Slot", "Min Price", "Weekly Avg", "Timestamp")
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show="headings")

        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=150)

        scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=scrollbar.set)

        self.results_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Export controls
        export_frame = ttk.Frame(parent)
        export_frame.pack(fill="x", padx=10, pady=5)

        ttk.Button(export_frame, text="Refresh Results",
                  command=self.refresh_results).pack(side="left", padx=5)

        ttk.Button(export_frame, text="Export to JSON",
                  command=self.export_results).pack(side="left", padx=5)

        ttk.Button(export_frame, text="Clear All Data",
                  command=self.clear_all_data).pack(side="left", padx=5)

    def update_status(self, message):
        """Update status message"""
        self.status_text.set(message)
        self.root.update_idletasks()

    def update_status_display(self):
        """Update configuration status display"""
        if self.market_automation.is_configured():
            self.config_status.config(text="✓ Configuration complete", foreground="green")
        else:
            self.config_status.config(text="⚠ Configuration incomplete", foreground="red")

    def connect_to_game(self):
        """Connect to the game"""
        if self.game_connector.connect_to_game():
            self.conn_status.config(text="✓ Connected", foreground="green")
            self.update_status("Connected to game")
        else:
            self.conn_status.config(text="✗ Failed to connect", foreground="red")
            self.update_status("Failed to connect to game")

    def select_first_slot(self):
        """Select the first inventory slot coordinates"""
        def callback(area):
            # Use center of selected area as click point
            x = area[0] + area[2] // 2
            y = area[1] + area[3] // 2
            self.inventory_grid.set_first_slot(x, y)
            self.update_status(f"First slot set to ({x}, {y})")
            self.update_status_display()

        self.area_selector.callback = callback
        self.area_selector.select_area("Select First Inventory Slot")

    def apply_offsets(self):
        """Apply slot offset values"""
        try:
            offset_x = int(self.offset_x_var.get())
            offset_y = int(self.offset_y_var.get())
            self.inventory_grid.set_slot_offsets(offset_x, offset_y)
            self.update_status(f"Slot offsets set to ({offset_x}, {offset_y})")
            self.update_status_display()
        except ValueError:
            messagebox.showerror("Error", "Please enter valid numbers for offsets")

    def select_market_drop_zone(self):
        """Select the market drop zone coordinates"""
        def callback(area):
            # Use center of selected area as click point
            x = area[0] + area[2] // 2
            y = area[1] + area[3] // 2
            self.market_automation.set_market_drop_zone((x, y))
            self.update_status(f"Market drop zone set to ({x}, {y})")
            self.update_status_display()

        self.area_selector.callback = callback
        self.area_selector.select_area("Select Market Drop Zone")

    def select_price_ocr_area(self):
        """Select the price OCR area"""
        def callback(area):
            self.market_automation.set_price_ocr_area(area)
            self.update_status(f"Price OCR area set to {area}")
            self.update_status_display()

        self.area_selector.callback = callback
        self.area_selector.select_area("Select Price OCR Area")

    def start_scan(self):
        """Start the market scan"""
        try:
            start_slot = int(self.start_slot_var.get())
            end_slot = int(self.end_slot_var.get())

            if start_slot < 1 or end_slot > 64 or start_slot > end_slot:
                messagebox.showerror("Error", "Invalid slot range. Use 1-64.")
                return

            # Apply timing settings
            click_delay = int(self.click_delay_var.get())
            ocr_delay = int(self.ocr_delay_var.get())
            self.market_automation.set_delays(click_delay, ocr_delay)

            if self.market_automation.start_scan(start_slot, end_slot):
                self.start_button.config(state="disabled")
                self.stop_button.config(state="normal")
                self.update_progress()

        except ValueError:
            messagebox.showerror("Error", "Please enter valid numbers")

    def stop_scan(self):
        """Stop the market scan"""
        self.market_automation.stop_scan()
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")

    def update_progress(self):
        """Update progress display"""
        if self.market_automation.running:
            progress = self.market_automation.get_progress()
            if progress:
                self.progress_var.set(progress['progress_percent'])
                self.progress_label.config(text=f"Scanning slot {progress['current_slot']} of {progress['total_slots']}")

            # Schedule next update
            self.root.after(1000, self.update_progress)
        else:
            self.progress_label.config(text="Scan completed")

    def refresh_results(self):
        """Refresh the results display"""
        # Clear existing items
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # Add current data
        latest_prices = self.market_data.get_all_latest_prices()
        for slot_key, data in latest_prices.items():
            slot_num = data['slot_number']
            latest = data['latest_price']

            self.results_tree.insert("", "end", values=(
                f"Slot {slot_num}",
                latest.get('min_price', 'N/A'),
                latest.get('weekly_avg', 'N/A'),
                latest.get('timestamp', 'N/A')[:19]  # Truncate timestamp
            ))

    def export_results(self):
        """Export results to JSON file"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            exported_file = self.market_data.export_to_file(filename)
            if exported_file:
                messagebox.showinfo("Export Complete", f"Data exported to {exported_file}")
            else:
                messagebox.showerror("Export Failed", "Failed to export data")

    def clear_all_data(self):
        """Clear all market data"""
        if messagebox.askyesno("Confirm", "Are you sure you want to clear all data?"):
            self.market_data.clear_all_data()
            self.market_data.save_data()
            self.refresh_results()
            self.update_status("All data cleared")

    def run(self):
        """Start the main event loop"""
        self.root.mainloop()
