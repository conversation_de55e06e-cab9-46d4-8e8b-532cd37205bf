# OCR engine for market price detection
# Adapted from unified_game_automation/core/ocr_engine.py

import pytesseract
from PIL import Image
import re
import os
import sys

class OCREngine:
    def __init__(self, status_callback=None):
        """Initialize the Tesseract OCR engine"""
        self.status_callback = status_callback

        # Set up Tesseract path
        import __main__
        if hasattr(__main__, '__file__'):
            main_script_dir = os.path.dirname(os.path.abspath(__main__.__file__))
        else:
            main_script_dir = os.getcwd()

        # Look for Tesseract in the unified_game_automation folder
        base_path = getattr(sys, "_MEIPASS", main_script_dir)
        
        # Try multiple possible paths for Tesseract
        possible_paths = [
            os.path.join(base_path, "Tesseract", "tesseract.exe"),
            os.path.join(base_path, "..", "unified_game_automation", "Tesseract", "tesseract.exe"),
            os.path.join(os.path.dirname(base_path), "unified_game_automation", "Tesseract", "tesseract.exe"),
            "tesseract"  # System PATH fallback
        ]
        
        tesseract_found = False
        for path in possible_paths:
            if os.path.exists(path) or path == "tesseract":
                pytesseract.pytesseract.tesseract_cmd = path
                tesseract_found = True
                if status_callback:
                    status_callback(f"Tesseract found at: {path}")
                break
        
        if not tesseract_found:
            if status_callback:
                status_callback("Warning: Tesseract not found, using system PATH")

    def update_status(self, message):
        """Update status via callback if available"""
        if self.status_callback:
            self.status_callback(message)

    def extract_text(self, image):
        """
        Extract text from image using Tesseract
        Args:
            image: PIL Image object
        Returns:
            Raw text string from OCR
        """
        try:
            if image is None:
                return ""

            text = pytesseract.image_to_string(image)
            return text
        except Exception as e:
            self.update_status(f"OCR error: {str(e)}")
            return ""

    def clean_price_text(self, text):
        """
        Clean OCR text for price detection
        Removes dots, spaces, commas and replaces '4' with '+' when appropriate
        """
        # Remove dots, spaces, and commas
        cleaned = re.sub(r'[.,\s]', '', text)
        
        # Replace '4' with '+' if no plus sign appears after numbers
        # This handles cases where '+' is misread as '4'
        if '+' not in cleaned:
            cleaned = re.sub(r'(\d)4', r'\1+', cleaned)
        
        return cleaned

    def extract_prices(self, text):
        """
        Extract min price and weekly average price from OCR text
        Returns:
            dict with 'min_price' and 'weekly_avg' keys, or None if not found
        """
        try:
            cleaned_text = self.clean_price_text(text)
            
            # Look for price patterns - numbers followed by optional 'k', 'm', etc.
            price_patterns = [
                r'(\d+(?:\.\d+)?[km]?)',  # Basic number with optional k/m suffix
                r'(\d+(?:,\d+)*)',        # Number with comma separators
                r'(\d+)'                  # Simple number
            ]
            
            prices = []
            for pattern in price_patterns:
                matches = re.findall(pattern, cleaned_text, re.IGNORECASE)
                if matches:
                    prices.extend(matches)
                    break
            
            if len(prices) >= 2:
                return {
                    'min_price': prices[0],
                    'weekly_avg': prices[1],
                    'raw_text': text,
                    'cleaned_text': cleaned_text
                }
            elif len(prices) == 1:
                return {
                    'min_price': prices[0],
                    'weekly_avg': None,
                    'raw_text': text,
                    'cleaned_text': cleaned_text
                }
            else:
                return None
                
        except Exception as e:
            self.update_status(f"Price extraction error: {str(e)}")
            return None

    def find_numbers(self, text):
        """Find all numbers in text"""
        return re.findall(r"\d+", text)
