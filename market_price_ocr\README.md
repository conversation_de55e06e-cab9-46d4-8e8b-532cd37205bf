# Market Price OCR Tool

A specialized OCR tool for automatically scanning market prices from your game inventory. This tool can scan all 64 inventory slots (8x8 grid) and extract current minimum prices and weekly average prices using OCR technology.

## Features

- **8x8 Inventory Grid Support**: Automatically calculates all 64 slot positions from first slot + offsets
- **Market Price Detection**: Extracts minimum price and weekly average price via OCR
- **Automated Scanning**: Click through all inventory slots automatically
- **JSON Data Export**: Save all price data with timestamps
- **Retry Logic**: Handles visual effects and OCR failures
- **Progress Tracking**: Real-time progress display
- **Configurable Timing**: Adjust delays for different system speeds

## Setup

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Tesseract OCR**: The tool will try to use Tesseract from the `unified_game_automation` folder. If not found, make sure Tesseract is installed on your system.

## How to Use

### 1. Prepare Your Inventory
- Place the items you want to price check in your inventory
- Use exactly **1 piece of each item**
- Items can be in any of the 64 slots (8x8 grid)

### 2. Configure the Tool

#### Game Connection
1. Start your game
2. Click "Connect to Game" in the Configuration tab

#### Inventory Grid Setup
1. Click "Select First Slot (Top-Left)" 
2. Select the top-left inventory slot in your game
3. Set the X and Y offsets between slots (default: 50px each)
4. Click "Apply Offsets"

#### Market Configuration
1. Open the market interface in your game
2. Click "Select Market Drop Zone" - this is where you drop items to see prices
3. Click "Select Price OCR Area" - select the area where prices are displayed

### 3. Start Scanning

1. Go to the "Scanning" tab
2. Set the slot range (1-64 for full scan)
3. Adjust timing if needed:
   - **Click Delay**: Time between clicks (default: 500ms)
   - **OCR Delay**: Time to wait before reading prices (default: 1000ms)
4. Click "Start Scan"

### 4. View Results

1. Go to the "Results" tab
2. Click "Refresh Results" to see latest data
3. Export to JSON file for external use
4. Clear data when needed

## How It Works

The automation process:

1. **Click Inventory Slot**: Selects an item from the specified slot
2. **Drop in Market**: Places the item in the market drop zone
3. **Wait for UI Update**: Allows market interface to load price data
4. **OCR Price Detection**: Captures and reads price information
5. **Save Data**: Stores prices with timestamp in JSON format
6. **Repeat**: Moves to next slot automatically

## Timing Configuration

The tool includes timing controls to handle different system speeds and visual effects:

- **Click Delay**: Adjust if clicks are happening too fast
- **OCR Delay**: Increase if market UI takes time to update prices
- **Retry Logic**: Automatically retries OCR if no valid prices detected

## Data Format

Price data is saved in JSON format:

```json
{
  "slot_1": {
    "slot_number": 1,
    "slot_name": "Slot 1",
    "price_history": [
      {
        "timestamp": "2025-01-28T19:30:00",
        "min_price": "1500k",
        "weekly_avg": "1800k",
        "raw_text": "Min: 1,500k\nWeekly Avg: 1,800k",
        "cleaned_text": "Min:1500k WeeklyAvg:1800k"
      }
    ]
  }
}
```

## Troubleshooting

### OCR Issues
- Ensure the price OCR area captures only the price text
- Increase OCR delay if visual effects interfere
- Check that Tesseract is properly installed

### Clicking Issues
- Verify inventory grid configuration
- Adjust click delays for slower systems
- Ensure game window is visible and active

### Connection Issues
- Make sure the game is running
- Try reconnecting if the game window changes

## Technical Details

- **OCR Engine**: Tesseract with text cleaning (removes dots, commas, spaces)
- **Game Connection**: Uses pywinauto to connect to D3D Window
- **Screen Capture**: BitBlt method for background window capture
- **Click Automation**: Precise coordinate-based clicking
- **Data Storage**: JSON format with timestamp tracking

## Files Structure

```
market_price_ocr/
├── core/                 # Core functionality
│   ├── game_connector.py # Game window connection and clicking
│   ├── ocr_engine.py     # OCR text extraction and processing
│   └── area_selector.py  # UI area selection tool
├── data/                 # Data management
│   ├── inventory_data.py # Inventory grid calculations
│   └── market_data.py    # Price data storage and JSON handling
├── automation/           # Automation logic
│   └── market_automation.py # Main scanning automation
├── ui/                   # User interface
│   └── market_window.py  # Main application window
├── main.py              # Application entry point
├── requirements.txt     # Python dependencies
└── README.md           # This file
```
