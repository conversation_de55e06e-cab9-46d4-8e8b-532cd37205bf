# Main entry point for Market Price OCR Tool

import sys
import os

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.game_connector import GameConnector
from core.ocr_engine import OCREngine
from core.area_selector import AreaSelector
from data.inventory_data import InventoryGrid
from data.market_data import MarketDataManager
from automation.market_automation import MarketAutomation
from ui.market_window import MarketWindow

def main():
    """Main entry point for the market price OCR tool"""
    print("Starting Market Price OCR Tool...")

    try:
        # Initialize core components
        game_connector = GameConnector()
        ocr_engine = OCREngine()
        inventory_grid = InventoryGrid()
        market_data = MarketDataManager()

        # Initialize automation
        market_automation = MarketAutomation(
            game_connector=game_connector,
            ocr_engine=ocr_engine,
            inventory_grid=inventory_grid,
            market_data=market_data
        )

        # Create main window first
        app = MarketWindow(
            game_connector=game_connector,
            ocr_engine=ocr_engine,
            inventory_grid=inventory_grid,
            market_data=market_data,
            market_automation=market_automation,
            area_selector=None  # Will be set after window creation
        )

        # Initialize area selector with the main window root
        area_selector = AreaSelector(app.root)
        app.area_selector = area_selector

        # Set up status callback for automation
        market_automation.status_callback = app.update_status

        # Run main app
        app.run()

    except Exception as e:
        print(f"Error starting application: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
