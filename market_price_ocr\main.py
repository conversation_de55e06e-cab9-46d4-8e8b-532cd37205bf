# Simple Market Price OCR Script

import sys
import os
import time

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.game_connector import GameConnector
from core.ocr_engine import OCREngine
from data.inventory_data import InventoryGrid
from data.market_data import MarketDataManager
from automation.market_automation import MarketAutomation
import config

def print_status(message):
    """Print status message with timestamp"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def main():
    """Main entry point for the market price OCR script"""
    print("=" * 50)
    print("Market Price OCR Script")
    print("=" * 50)

    try:
        # Initialize core components
        print_status("Initializing components...")
        game_connector = GameConnector(status_callback=print_status)
        ocr_engine = OCREngine(status_callback=print_status)
        inventory_grid = InventoryGrid()
        market_data = MarketDataManager()

        # Configure inventory grid with hardcoded values
        print_status("Configuring inventory grid...")
        inventory_grid.set_first_slot(*config.FIRST_SLOT_COORDS)
        inventory_grid.set_slot_offsets(config.SLOT_OFFSET_X, config.SLOT_OFFSET_Y)

        # Initialize automation
        market_automation = MarketAutomation(
            game_connector=game_connector,
            ocr_engine=ocr_engine,
            inventory_grid=inventory_grid,
            market_data=market_data,
            status_callback=print_status
        )

        # Configure automation with hardcoded values
        print_status("Configuring automation...")
        market_automation.set_open_market_coords(config.OPEN_MARKET_COORDS)
        market_automation.set_close_market_coords(config.CLOSE_MARKET_COORDS)
        market_automation.set_market_drop_zone(config.MARKET_DROP_ZONE)
        market_automation.set_price_ocr_area(config.PRICE_OCR_AREA)
        market_automation.set_delays(config.CLICK_DELAY_MS, config.OCR_DELAY_MS)

        # Connect to game
        print_status("Connecting to game...")
        if not game_connector.connect_to_game():
            print_status("❌ Failed to connect to game. Make sure the game is running.")
            input("Press Enter to exit...")
            return

        print_status("✅ Connected to game successfully!")

        # Ask user for slot range
        print("\nConfiguration:")
        print(f"First slot coordinates: {config.FIRST_SLOT_COORDS}")
        print(f"Slot offsets: X={config.SLOT_OFFSET_X}, Y={config.SLOT_OFFSET_Y}")
        print(f"Open market (right-click): {config.OPEN_MARKET_COORDS}")
        print(f"Close market (left-click): {config.CLOSE_MARKET_COORDS}")
        print(f"Market drop zone: {config.MARKET_DROP_ZONE}")
        print(f"Price OCR area: {config.PRICE_OCR_AREA}")
        print()

        try:
            start_slot = int(input("Enter start slot (1-64): ") or "1")
            end_slot = int(input("Enter end slot (1-64): ") or "64")

            if start_slot < 1 or end_slot > 64 or start_slot > end_slot:
                print("❌ Invalid slot range!")
                input("Press Enter to exit...")
                return

        except ValueError:
            print("❌ Invalid input!")
            input("Press Enter to exit...")
            return

        # Start scanning
        print_status(f"Starting scan from slot {start_slot} to {end_slot}...")
        print("Press Ctrl+C to stop scanning")
        print()

        if market_automation.start_scan(start_slot, end_slot):
            # Wait for completion
            while market_automation.running:
                time.sleep(1)

        # Show results
        print("\n" + "=" * 50)
        print("SCAN RESULTS")
        print("=" * 50)

        latest_prices = market_data.get_all_latest_prices()
        if latest_prices:
            for slot_key in sorted(latest_prices.keys(), key=lambda x: int(x.split('_')[1])):
                data = latest_prices[slot_key]
                latest = data['latest_price']
                weekly_avg = latest.get('weekly_avg') or 'N/A'
                min_price = latest.get('min_price') or 'N/A'
                print(f"Slot {data['slot_number']:2d}: Weekly Avg = {weekly_avg:>8s}, Min Price = {min_price:>8s}")
        else:
            print("No price data collected.")

        # Save data
        if market_data.save_data():
            print_status(f"✅ Data saved to {market_data.data_file}")
        else:
            print_status("❌ Failed to save data")

        print("\nScan completed!")
        input("Press Enter to exit...")

    except KeyboardInterrupt:
        print_status("🛑 Scan stopped by user")
        if 'market_automation' in locals():
            market_automation.stop_scan()
        input("Press Enter to exit...")

    except Exception as e:
        print_status(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
