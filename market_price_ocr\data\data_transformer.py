# Data transformer to convert slot-based data to website format

from datetime import datetime
import item_mapping

class DataTransformer:
    def __init__(self):
        """Initialize the data transformer"""
        self.slot_mapping = item_mapping.SLOT_TO_ITEM_MAPPING
        self.use_min_price = item_mapping.USE_MIN_PRICE
        self.skip_empty_slots = item_mapping.SKIP_EMPTY_SLOTS
        
    def transform_to_website_format(self, market_data_manager):
        """
        Transform slot-based market data to website format
        Args:
            market_data_manager: MarketDataManager instance with price data
        Returns:
            Dictionary in website format
        """
        # Get all latest prices
        latest_prices = market_data_manager.get_all_latest_prices()
        
        if not latest_prices:
            return {
                "timestamp": datetime.now().isoformat(),
                "prices": {}
            }
        
        # Find the most recent timestamp
        most_recent_timestamp = None
        for slot_data in latest_prices.values():
            slot_timestamp = slot_data['latest_price'].get('timestamp')
            if slot_timestamp:
                if most_recent_timestamp is None or slot_timestamp > most_recent_timestamp:
                    most_recent_timestamp = slot_timestamp
        
        # Use current time if no timestamps found
        if most_recent_timestamp is None:
            most_recent_timestamp = datetime.now().isoformat()
        
        # Build prices dictionary
        prices = {}
        
        for slot_key, slot_data in latest_prices.items():
            slot_number = slot_data['slot_number']
            latest_price_data = slot_data['latest_price']
            
            # Get item name from mapping
            item_name = self.slot_mapping.get(slot_number, "")
            
            # Skip if no item name and skip_empty_slots is True
            if self.skip_empty_slots and not item_name:
                continue
                
            # Use slot name as fallback if no item name
            if not item_name:
                item_name = f"Slot {slot_number}"
            
            # Get price value
            if self.use_min_price:
                price_value = latest_price_data.get('min_price')
            else:
                price_value = latest_price_data.get('weekly_avg')
            
            # Convert to integer if possible
            if price_value:
                try:
                    price_value = int(price_value)
                except (ValueError, TypeError):
                    price_value = 0
            else:
                price_value = 0
            
            prices[item_name] = price_value
        
        return {
            "timestamp": most_recent_timestamp,
            "prices": prices
        }
    
    def save_website_format(self, market_data_manager, filename="website_prices.json"):
        """
        Save data in website format to JSON file
        Args:
            market_data_manager: MarketDataManager instance
            filename: Output filename
        Returns:
            True if successful, False otherwise
        """
        try:
            import json
            
            website_data = self.transform_to_website_format(market_data_manager)
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(website_data, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception as e:
            print(f"Error saving website format: {e}")
            return False
    
    def get_mapping_summary(self):
        """Get a summary of current slot mappings"""
        mapped_slots = []
        unmapped_slots = []
        
        for slot_num in range(1, 65):
            item_name = self.slot_mapping.get(slot_num, "")
            if item_name:
                mapped_slots.append(f"Slot {slot_num}: {item_name}")
            else:
                unmapped_slots.append(slot_num)
        
        return {
            "mapped_count": len(mapped_slots),
            "unmapped_count": len(unmapped_slots),
            "mapped_slots": mapped_slots,
            "unmapped_slots": unmapped_slots
        }
