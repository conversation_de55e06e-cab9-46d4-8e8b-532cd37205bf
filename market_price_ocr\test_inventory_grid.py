# Test inventory grid coordinate calculation

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from data.inventory_data import InventoryGrid

def test_grid_coordinates():
    """Test that inventory grid calculates coordinates correctly"""
    print("Testing Inventory Grid Coordinate Calculation...")
    
    grid = InventoryGrid()
    
    # Example configuration
    grid.set_first_slot(100, 200)  # Top-left slot
    grid.set_slot_offsets(50, 60)  # 50px horizontal, 60px vertical spacing
    
    print(f"First slot: {grid.first_slot_coords}")
    print(f"Offsets: X={grid.slot_offset_x}, Y={grid.slot_offset_y}")
    print()
    
    # Test first row (slots 1-8)
    print("First row (slots 1-8):")
    for slot in range(1, 9):
        coords = grid.get_slot_coordinates(slot)
        print(f"  Slot {slot}: {coords}")
    
    print()
    
    # Test second row (slots 9-16)
    print("Second row (slots 9-16):")
    for slot in range(9, 17):
        coords = grid.get_slot_coordinates(slot)
        print(f"  Slot {slot}: {coords}")
    
    print()
    
    # Test specific slots that were problematic
    print("Specific slots:")
    for slot in [4, 5, 6, 7]:
        coords = grid.get_slot_coordinates(slot)
        print(f"  Slot {slot}: {coords}")
    
    print()
    
    # Test last row (slots 57-64)
    print("Last row (slots 57-64):")
    for slot in range(57, 65):
        coords = grid.get_slot_coordinates(slot)
        print(f"  Slot {slot}: {coords}")

if __name__ == "__main__":
    test_grid_coordinates()
