# Inventory slot coordinate calculation and management

class InventoryGrid:
    def __init__(self):
        """Initialize 8x8 inventory grid calculator"""
        self.grid_size = 8  # 8x8 = 64 slots
        self.first_slot_coords = None  # (x, y) coordinates of slot 1
        self.slot_offset_x = 0  # Horizontal distance between slots
        self.slot_offset_y = 0  # Vertical distance between slots
        
    def set_first_slot(self, x, y):
        """Set the coordinates of the first inventory slot (top-left)"""
        self.first_slot_coords = (x, y)
        
    def set_slot_offsets(self, offset_x, offset_y):
        """Set the offset between inventory slots"""
        self.slot_offset_x = offset_x
        self.slot_offset_y = offset_y
        
    def get_slot_coordinates(self, slot_number):
        """
        Get coordinates for a specific slot number (1-64)
        Args:
            slot_number: Integer from 1 to 64
        Returns:
            (x, y) coordinates or None if not configured
        """
        if not self.first_slot_coords:
            return None
            
        if slot_number < 1 or slot_number > 64:
            return None
            
        # Convert slot number to grid position (0-based)
        slot_index = slot_number - 1
        row = slot_index // self.grid_size
        col = slot_index % self.grid_size
        
        # Calculate coordinates
        x = self.first_slot_coords[0] + (col * self.slot_offset_x)
        y = self.first_slot_coords[1] + (row * self.slot_offset_y)
        
        return (x, y)
        
    def get_all_slot_coordinates(self):
        """
        Get coordinates for all 64 slots
        Returns:
            Dictionary with slot numbers as keys and (x, y) coordinates as values
        """
        if not self.first_slot_coords:
            return {}
            
        slots = {}
        for slot_num in range(1, 65):  # 1 to 64
            coords = self.get_slot_coordinates(slot_num)
            if coords:
                slots[slot_num] = coords
                
        return slots
        
    def get_slot_name(self, slot_number):
        """Get display name for a slot"""
        return f"Slot {slot_number}"
        
    def is_configured(self):
        """Check if the inventory grid is properly configured"""
        return (self.first_slot_coords is not None and 
                self.slot_offset_x != 0 and 
                self.slot_offset_y != 0)
                
    def get_configuration(self):
        """Get current configuration as dictionary"""
        return {
            'first_slot_coords': self.first_slot_coords,
            'slot_offset_x': self.slot_offset_x,
            'slot_offset_y': self.slot_offset_y,
            'grid_size': self.grid_size
        }
        
    def set_configuration(self, config):
        """Set configuration from dictionary"""
        if 'first_slot_coords' in config:
            self.first_slot_coords = config['first_slot_coords']
        if 'slot_offset_x' in config:
            self.slot_offset_x = config['slot_offset_x']
        if 'slot_offset_y' in config:
            self.slot_offset_y = config['slot_offset_y']
        if 'grid_size' in config:
            self.grid_size = config['grid_size']
