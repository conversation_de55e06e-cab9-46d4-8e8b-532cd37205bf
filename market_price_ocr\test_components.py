# Test script to verify core components

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_inventory_grid():
    """Test inventory grid calculations"""
    print("Testing Inventory Grid...")
    
    from data.inventory_data import InventoryGrid
    
    grid = InventoryGrid()
    
    # Test configuration
    grid.set_first_slot(100, 200)
    grid.set_slot_offsets(50, 60)
    
    # Test slot calculations
    slot_1 = grid.get_slot_coordinates(1)
    slot_2 = grid.get_slot_coordinates(2)
    slot_9 = grid.get_slot_coordinates(9)  # First slot of second row
    
    print(f"Slot 1: {slot_1}")  # Should be (100, 200)
    print(f"Slot 2: {slot_2}")  # Should be (150, 200)
    print(f"Slot 9: {slot_9}")  # Should be (100, 260)
    
    assert slot_1 == (100, 200), f"Expected (100, 200), got {slot_1}"
    assert slot_2 == (150, 200), f"Expected (150, 200), got {slot_2}"
    assert slot_9 == (100, 260), f"Expected (100, 260), got {slot_9}"
    
    print("✓ Inventory Grid test passed!")

def test_market_data():
    """Test market data management"""
    print("\nTesting Market Data...")
    
    from data.market_data import MarketDataManager
    
    # Use a test file
    manager = MarketDataManager("test_market_data.json")
    
    # Test adding price data
    price_info = {
        'min_price': '1500k',
        'weekly_avg': '1800k',
        'raw_text': 'Min: 1,500k\nWeekly Avg: 1,800k',
        'cleaned_text': 'Min:1500k WeeklyAvg:1800k'
    }
    
    manager.add_price_data(1, "Slot 1", price_info)
    
    # Test retrieval
    latest = manager.get_latest_price(1)
    assert latest is not None, "Should have price data for slot 1"
    assert latest['min_price'] == '1500k', f"Expected '1500k', got {latest['min_price']}"
    
    # Test progress
    progress = manager.get_scan_progress()
    assert progress['scanned_slots'] == 1, f"Expected 1 scanned slot, got {progress['scanned_slots']}"
    
    # Clean up test file
    if os.path.exists("test_market_data.json"):
        os.remove("test_market_data.json")
    
    print("✓ Market Data test passed!")

def test_ocr_engine():
    """Test OCR engine text processing"""
    print("\nTesting OCR Engine...")
    
    from core.ocr_engine import OCREngine
    
    ocr = OCREngine()
    
    # Test text cleaning
    test_text = "Min: 1,500.4k\nWeekly Avg: 1,800k"
    cleaned = ocr.clean_price_text(test_text)
    print(f"Original: {test_text}")
    print(f"Cleaned: {cleaned}")
    
    # Test price extraction
    price_info = ocr.extract_prices(test_text)
    print(f"Extracted prices: {price_info}")
    
    assert price_info is not None, "Should extract price information"
    assert price_info['min_price'] is not None, "Should have min price"
    
    print("✓ OCR Engine test passed!")

def main():
    """Run all tests"""
    print("Running Market Price OCR Component Tests...\n")
    
    try:
        test_inventory_grid()
        test_market_data()
        test_ocr_engine()
        
        print("\n🎉 All tests passed! The system is ready to use.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
